version: '3.8'

services:
  # Weaviate Core Database
  weaviate:
    image: semitechnologies/weaviate:1.25.0
    container_name: hvac-weaviate
    ports:
      - "8080:8080"
      - "50051:50051"  # gRPC port
    environment:
      # Basic Configuration
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      
      # Vectorizer Configuration
      DEFAULT_VECTORIZER_MODULE: 'text2vec-transformers'
      ENABLE_MODULES: 'text2vec-transformers,generative-openai,qna-transformers,backup-filesystem'
      TRANSFORMERS_INFERENCE_API: 'http://t2v-transformers:8080'
      
      # Resource Management (Optimized for 80GB RAM)
      LIMIT_RESOURCES: 'true'
      GOMEMLIMIT: '30GB'  # Leave 20GB for OS and other services
      GOMAXPROCS: '5'     # Use 7 of 8 CPU cores
      
      # Performance Optimization
      QUERY_MAXIMUM_RESULTS: 10000
      TRACK_VECTOR_DIMENSIONS: 'true'
      
      # Cluster Configuration
      CLUSTER_HOSTNAME: 'node1'
      CLUSTER_GOSSIP_BIND_PORT: '7100'
      CLUSTER_DATA_BIND_PORT: '7101'
      
      # Backup Configuration
      BACKUP_FILESYSTEM_PATH: '/var/lib/weaviate/backups'
      
      # Monitoring
      PROMETHEUS_MONITORING_ENABLED: 'true'
      PROMETHEUS_MONITORING_PORT: '2112'
      
    volumes:
      - ./weaviate_data:/var/lib/weaviate
      - ./weaviate_backups:/var/lib/weaviate/backups
    restart: on-failure:3
    depends_on:
      - t2v-transformers
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 32GB  # Hard limit slightly above GOMEMLIMIT
        reservations:
          memory: 40GB
    networks:
      - weaviate-network

  # Local Embedding Model (High Quality)
  t2v-transformers:
    image: cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2
    container_name: hvac-embeddings
    environment:
      ENABLE_CUDA: '0'  # Use GPU acceleration
      CUDA_VISIBLE_DEVICES: '1'
      
      # Model Configuration
      MODEL_NAME: 'sentence-transformers/all-mpnet-base-v2'
      MAX_SEQUENCE_LENGTH: '512'
      BATCH_SIZE: '32'
      
      # Performance Tuning
      WORKERS: '4'
      TIMEOUT: '60'
      
      # Memory Management
      PYTORCH_CUDA_ALLOC_CONF: 'max_split_size_mb:512'
      
    deploy:
      resources:
        limits:
          memory: 8GB
        reservations:
          memory: 4GB
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # Longer startup for model loading
    networks:
      - weaviate-network

  # Alternative Multilingual Model (for Polish support)
  t2v-multilingual:
    image: cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-paraphrase-multilingual-mpnet-base-v2
    container_name: hvac-embeddings-multilingual
    ports:
      - "8081:8080"  # Different port for alternative model
    environment:
      ENABLE_CUDA: '1'
      CUDA_VISIBLE_DEVICES: '1'  # Use second GPU if available
      
      MODEL_NAME: 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'
      MAX_SEQUENCE_LENGTH: '512'
      BATCH_SIZE: '16'  # Smaller batch for multilingual model
      
      WORKERS: '2'
      TIMEOUT: '60'
      
    deploy:
      resources:
        limits:
          memory: 6GB
        reservations:
          memory: 3GB
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: on-failure:3
    profiles:
      - multilingual  # Optional service, activate with --profile multilingual
    networks:
      - weaviate-network

  # Monitoring and Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Redis for Caching and Session Management
  redis:
    image: redis:7-alpine
    container_name: hvac-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 4gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - weaviate-network

  # Backup Service
  backup-service:
    image: alpine:latest
    container_name: hvac-backup
    volumes:
      - ./weaviate_data:/source:ro
      - ./backups:/backup
      - ./scripts/backup.sh:/backup.sh:ro
    command: |
      sh -c "
        apk add --no-cache curl &&
        chmod +x /backup.sh &&
        crond -f
      "
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - WEAVIATE_URL=http://weaviate:8080
      - BACKUP_RETENTION_DAYS=30
    restart: unless-stopped
    profiles:
      - backup
    networks:
      - weaviate-network

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  redis_data:
    driver: local

networks:
  weaviate-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
